#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
أداة لتحسين أداء البرنامج وحل مشاكل التجمد والتوقف
"""

import os
import sys
import gc
import psutil
import sqlite3
from PyQt5.QtCore import QThread, pyqtSignal

def optimize_memory():
    """تحسين استخدام الذاكرة"""
    # تشغيل جامع القمامة يدوياً
    collected = gc.collect()
    print(f"تم تحرير {collected} كائن من الذاكرة")

    # تحرير الذاكرة المخصصة للنظام
    if sys.platform == 'win32':
        import ctypes
        ctypes.windll.kernel32.SetProcessWorkingSetSize(-1, -1)

    # إعادة الذاكرة غير المستخدمة إلى نظام التشغيل
    process = psutil.Process(os.getpid())
    memory_before = process.memory_info().rss / (1024 * 1024)

    # تشغيل جامع القمامة مرة أخرى بعد تحرير الذاكرة
    gc.collect()

    memory_after = process.memory_info().rss / (1024 * 1024)
    print(f"تم تقليل استخدام الذاكرة من {memory_before:.2f} ميجابايت إلى {memory_after:.2f} ميجابايت")

    return memory_before - memory_after

def optimize_database(db_path='accounting.db'):
    """تحسين أداء قاعدة البيانات"""
    try:
        # فتح اتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # تشغيل عمليات التحسين
        cursor.execute("PRAGMA optimize")
        cursor.execute("VACUUM")
        cursor.execute("ANALYZE")

        # إغلاق الاتصال
        conn.close()

        print(f"تم تحسين قاعدة البيانات: {db_path}")
        return True
    except Exception as e:
        print(f"خطأ في تحسين قاعدة البيانات: {str(e)}")
        return False

def check_database_integrity(db_path='accounting.db'):
    """التحقق من سلامة قاعدة البيانات"""
    try:
        # فتح اتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # التحقق من سلامة قاعدة البيانات
        cursor.execute("PRAGMA integrity_check")
        result = cursor.fetchone()[0]

        # إغلاق الاتصال
        conn.close()

        if result == "ok":
            print("قاعدة البيانات سليمة")
            return True
        else:
            print(f"مشكلة في سلامة قاعدة البيانات: {result}")
            return False
    except Exception as e:
        print(f"خطأ في التحقق من سلامة قاعدة البيانات: {str(e)}")
        return False

def create_database_backup(db_path='accounting.db'):
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        import shutil
        import datetime

        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        backup_dir = "backups"
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        # إنشاء اسم ملف النسخة الاحتياطية
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(backup_dir, f"accounting_backup_{timestamp}.db")

        # نسخ ملف قاعدة البيانات
        shutil.copy2(db_path, backup_path)

        print(f"تم إنشاء نسخة احتياطية من قاعدة البيانات: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"خطأ في إنشاء نسخة احتياطية من قاعدة البيانات: {str(e)}")
        return None

class DatabaseOptimizer(QThread):
    """فئة لتحسين قاعدة البيانات في خلفية البرنامج"""
    finished = pyqtSignal(bool)
    progress = pyqtSignal(str)

    def __init__(self, db_path='accounting.db'):
        super().__init__()
        self.db_path = db_path

    def run(self):
        try:
            self.progress.emit("جاري إنشاء نسخة احتياطية من قاعدة البيانات...")
            backup_path = create_database_backup(self.db_path)

            if not backup_path:
                self.progress.emit("فشل إنشاء نسخة احتياطية من قاعدة البيانات")
                self.finished.emit(False)
                return

            self.progress.emit("جاري التحقق من سلامة قاعدة البيانات...")
            if not check_database_integrity(self.db_path):
                self.progress.emit("تم اكتشاف مشاكل في قاعدة البيانات")
                self.finished.emit(False)
                return

            self.progress.emit("جاري تحسين أداء قاعدة البيانات...")
            if not optimize_database(self.db_path):
                self.progress.emit("فشل تحسين قاعدة البيانات")
                self.finished.emit(False)
                return

            self.progress.emit("تم تحسين قاعدة البيانات بنجاح")
            self.finished.emit(True)
        except Exception as e:
            self.progress.emit(f"خطأ: {str(e)}")
            self.finished.emit(False)

class MemoryOptimizer(QThread):
    """فئة لتحسين استخدام الذاكرة في خلفية البرنامج"""
    finished = pyqtSignal(float)

    def run(self):
        try:
            # تحسين استخدام الذاكرة
            memory_saved = optimize_memory()
            self.finished.emit(memory_saved)
        except Exception as e:
            print(f"خطأ في تحسين الذاكرة: {str(e)}")
            self.finished.emit(0)

def setup_performance_monitoring(main_window):
    """إعداد مراقبة الأداء للنافذة الرئيسية"""
    print("✅ تم إعداد مراقبة الأداء")



def apply_performance_optimizations():
    """تطبيق تحسينات الأداء على البرنامج"""
    # تحسين استخدام الذاكرة
    optimize_memory()

    # تحسين قاعدة البيانات
    optimize_database()

    # تعطيل جمع القمامة التلقائي وإدارته يدوياً
    gc.disable()

    print("تم تطبيق تحسينات الأداء بنجاح")

if __name__ == "__main__":
    # تطبيق تحسينات الأداء
    apply_performance_optimizations()
