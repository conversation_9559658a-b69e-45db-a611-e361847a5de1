from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QTextEdit, QDoubleSpinBox, QPushButton,
                            QLabel, QGroupBox, QTableWidget, QTableWidgetItem,
                            QAbstractItemView)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor

from database import Supplier, Expense
from ui.unified_styles import UnifiedStyles, BaseDialog
from utils import show_error_message, show_info_message

class SupplierDialog(BaseDialog):
    """نافذة حوار لإضافة أو تعديل مورد"""

    def __init__(self, parent=None, supplier=None):
        title = "تعديل بيانات المورد" if supplier else "إضافة مورد جديد"
        super().__init__(parent, title)
        self.supplier = supplier
        self.session = parent.session if parent else None
        self.init_ui()
        if self.supplier:
            self.load_supplier_data()

    def init_ui(self):
        """إنشاء واجهة الحوار"""
        self.setMinimumSize(500, 400)
        layout = QVBoxLayout()

        # إنشاء النموذج
        form_layout = QFormLayout()

        # حقل الاسم
        self.name_edit = QLineEdit()
        self.name_edit.setStyleSheet(UnifiedStyles.get_input_style())
        form_layout.addRow("اسم المورد:", self.name_edit)

        # حقل الهاتف
        self.phone_edit = QLineEdit()
        self.phone_edit.setStyleSheet(UnifiedStyles.get_input_style())
        form_layout.addRow("رقم الهاتف:", self.phone_edit)

        # حقل البريد الإلكتروني
        self.email_edit = QLineEdit()
        self.email_edit.setStyleSheet(UnifiedStyles.get_input_style())
        form_layout.addRow("البريد الإلكتروني:", self.email_edit)

        # حقل العنوان
        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(80)
        self.address_edit.setStyleSheet(UnifiedStyles.get_input_style())
        form_layout.addRow("العنوان:", self.address_edit)

        # حقل الرصيد
        self.balance_edit = QDoubleSpinBox()
        self.balance_edit.setRange(-999999.99, 999999.99)
        self.balance_edit.setDecimals(2)
        self.balance_edit.setStyleSheet(UnifiedStyles.get_input_style())
        form_layout.addRow("الرصيد:", self.balance_edit)

        # حقل الملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setStyleSheet(UnifiedStyles.get_input_style())
        form_layout.addRow("الملاحظات:", self.notes_edit)

        layout.addLayout(form_layout)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()
        
        save_button = QPushButton("حفظ")
        save_button.setStyleSheet(UnifiedStyles.get_button_style('success'))
        save_button.clicked.connect(self.save_supplier)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.setStyleSheet(UnifiedStyles.get_button_style('secondary'))
        cancel_button.clicked.connect(self.reject)

        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(cancel_button)
        layout.addLayout(buttons_layout)

        self.setLayout(layout)

    def load_supplier_data(self):
        """تحميل بيانات المورد للتعديل"""
        if self.supplier:
            self.name_edit.setText(self.supplier.name or "")
            self.phone_edit.setText(self.supplier.phone or "")
            self.email_edit.setText(self.supplier.email or "")
            self.address_edit.setPlainText(self.supplier.address or "")
            self.balance_edit.setValue(self.supplier.balance or 0.0)
            self.notes_edit.setPlainText(self.supplier.notes or "")

    def save_supplier(self):
        """حفظ بيانات المورد"""
        # التحقق من صحة البيانات
        if not self.name_edit.text().strip():
            show_error_message("خطأ", "يرجى إدخال اسم المورد")
            return

        try:
            if self.supplier:
                # تعديل مورد موجود
                self.supplier.name = self.name_edit.text().strip()
                self.supplier.phone = self.phone_edit.text().strip()
                self.supplier.email = self.email_edit.text().strip()
                self.supplier.address = self.address_edit.toPlainText().strip()
                self.supplier.balance = self.balance_edit.value()
                self.supplier.notes = self.notes_edit.toPlainText().strip()
            else:
                # إضافة مورد جديد
                self.supplier = Supplier(
                    name=self.name_edit.text().strip(),
                    phone=self.phone_edit.text().strip(),
                    email=self.email_edit.text().strip(),
                    address=self.address_edit.toPlainText().strip(),
                    balance=self.balance_edit.value(),
                    notes=self.notes_edit.toPlainText().strip()
                )
                self.session.add(self.supplier)

            self.session.commit()
            show_info_message("تم", "تم حفظ بيانات المورد بنجاح")
            self.accept()

        except Exception as e:
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")

class SupplierFinancialDetailsDialog(BaseDialog):
    """نافذة عرض التفاصيل المالية للمورد"""

    def __init__(self, parent=None, supplier=None, session=None):
        super().__init__(parent, f"التفاصيل المالية - {supplier.name if supplier else 'مورد'}")
        self.supplier = supplier
        self.session = session
        self.init_ui()
        self.load_financial_data()

    def init_ui(self):
        """إنشاء واجهة الحوار"""
        self.setMinimumSize(600, 500)
        layout = QVBoxLayout()

        # معلومات المورد الأساسية
        info_group = QGroupBox("معلومات المورد")
        info_layout = QFormLayout()

        self.name_label = QLabel()
        self.phone_label = QLabel()
        self.email_label = QLabel()
        self.balance_label = QLabel()

        info_layout.addRow("الاسم:", self.name_label)
        info_layout.addRow("الهاتف:", self.phone_label)
        info_layout.addRow("البريد:", self.email_label)
        info_layout.addRow("الرصيد الحالي:", self.balance_label)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        # جدول المعاملات المالية
        transactions_group = QGroupBox("سجل المعاملات المالية")
        transactions_layout = QVBoxLayout()

        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(4)
        self.transactions_table.setHorizontalHeaderLabels([
            "التاريخ", "الوصف", "المبلغ", "النوع"
        ])
        self.transactions_table.horizontalHeader().setStretchLastSection(True)
        self.transactions_table.setAlternatingRowColors(True)
        self.transactions_table.setSelectionBehavior(QAbstractItemView.SelectRows)

        transactions_layout.addWidget(self.transactions_table)
        transactions_group.setLayout(transactions_layout)
        layout.addWidget(transactions_group)

        # زر الإغلاق
        close_button = QPushButton("إغلاق")
        close_button.setStyleSheet(UnifiedStyles.get_button_style('secondary'))
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)

        self.setLayout(layout)

    def load_financial_data(self):
        """تحميل البيانات المالية للمورد"""
        if not self.supplier:
            return

        # تحميل المعلومات الأساسية
        self.name_label.setText(self.supplier.name)
        self.phone_label.setText(self.supplier.phone or "غير محدد")
        self.email_label.setText(self.supplier.email or "غير محدد")
        
        # تنسيق الرصيد مع الألوان
        balance = self.supplier.balance
        if balance > 0:
            self.balance_label.setText(f"<span style='color: green; font-weight: bold;'>{balance:.2f} ر.س (للمورد)</span>")
        elif balance < 0:
            self.balance_label.setText(f"<span style='color: red; font-weight: bold;'>{abs(balance):.2f} ر.س (على المورد)</span>")
        else:
            self.balance_label.setText("<span style='color: gray;'>0.00 ر.س (متوازن)</span>")

        # تحميل المعاملات المالية
        try:
            expenses = self.session.query(Expense).filter_by(supplier_id=self.supplier.id).order_by(Expense.date.desc()).all()
            
            self.transactions_table.setRowCount(len(expenses))
            
            for row, expense in enumerate(expenses):
                # التاريخ
                date_item = QTableWidgetItem(expense.date.strftime("%Y-%m-%d") if expense.date else "غير محدد")
                self.transactions_table.setItem(row, 0, date_item)
                
                # الوصف
                desc_item = QTableWidgetItem(expense.description or "مصروف")
                self.transactions_table.setItem(row, 1, desc_item)
                
                # المبلغ
                amount_item = QTableWidgetItem(f"{expense.amount:.2f} ر.س")
                amount_item.setTextAlignment(Qt.AlignCenter)
                if expense.amount > 0:
                    amount_item.setForeground(QColor("red"))
                self.transactions_table.setItem(row, 2, amount_item)
                
                # النوع
                type_item = QTableWidgetItem("مصروف")
                type_item.setTextAlignment(Qt.AlignCenter)
                self.transactions_table.setItem(row, 3, type_item)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحميل المعاملات: {str(e)}")


