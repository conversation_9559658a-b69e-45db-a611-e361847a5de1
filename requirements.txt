# متطلبات برنامج المحاسبة للمكتب الهندسي
# Engineering Office Accounting System Requirements

# واجهة المستخدم الرسومية
# Graphical User Interface
PyQt5>=5.15.0

# قاعدة البيانات
# Database ORM
SQLAlchemy>=1.4.0

# مراقبة النظام والأداء
# System and performance monitoring
psutil>=5.8.0

# معالجة التواريخ والأوقات (مدمج مع Python)
# Date and time handling (built-in with Python)
# datetime

# معالجة الملفات والمسارات (مدمج مع Python)
# File and path handling (built-in with Python)
# os
# sys

# معالجة JSON (مدمج مع Python)
# JSON handling (built-in with Python)
# json

# معالجة CSV (مدمج مع Python)
# CSV handling (built-in with Python)
# csv

# معالجة التعبيرات النمطية (مدمج مع Python)
# Regular expressions (built-in with Python)
# re

# إدارة الذاكرة (مدمج مع Python)
# Memory management (built-in with Python)
# gc

# معلومات النظام (مدمج مع Python)
# System information (built-in with Python)
# platform

# معالجة الأخطاء (مدمج مع Python)
# Error handling (built-in with Python)
# traceback

# ملاحظات التثبيت:
# Installation Notes:
# 
# 1. تأكد من تثبيت Python 3.7 أو أحدث
#    Make sure Python 3.7+ is installed
#
# 2. قم بتثبيت المتطلبات باستخدام:
#    Install requirements using:
#    pip install -r requirements.txt
#
# 3. في حالة مواجهة مشاكل مع PyQt5، جرب:
#    If you face issues with PyQt5, try:
#    pip install PyQt5 --force-reinstall
#
# 4. للتأكد من التثبيت الصحيح:
#    To verify correct installation:
#    python -c "import PyQt5; import sqlalchemy; print('All dependencies installed successfully')"
