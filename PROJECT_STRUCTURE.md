# 🏗️ هيكل المشروع - برنامج المحاسبة للمكتب الهندسي

## 📁 **التنظيم العام للملفات:**

```
📦 accounting-system/
├── 📄 main.py                    # نقطة البداية الرئيسية
├── 📄 database.py               # نماذج قاعدة البيانات
├── 📄 performance_optimizer.py  # تحسين الأداء
├── 📄 README.md                 # دليل المستخدم
├── 📄 PROJECT_STRUCTURE.md      # هيكل المشروع (هذا الملف)
├── 📄 requirements.txt          # متطلبات النظام
├── 📄 accounting.db             # قاعدة البيانات
├── 📄 clients_table_state.json  # حالة جدول العملاء
├── 📁 ui/                       # واجهات المستخدم
│   ├── 📄 main_window.py        # النافذة الرئيسية
│   ├── 📄 unified_styles.py     # الأنماط الموحدة
│   ├── 📄 clients.py            # إدارة العملاء
│   ├── 📄 suppliers.py          # إدارة الموردين
│   ├── 📄 employees.py          # إدارة الموظفين
│   ├── 📄 expenses.py           # إدارة المصروفات
│   ├── 📄 revenues.py           # إدارة الإيرادات
│   ├── 📄 invoices.py           # إدارة الفواتير
│   ├── 📄 projects.py           # إدارة المشاريع
│   ├── 📄 properties.py         # إدارة العقارات
│   ├── 📄 inventory.py          # إدارة المخزون
│   ├── 📄 reports.py            # التقارير
│   ├── 📄 notifications.py      # الإشعارات
│   ├── 📄 reminders.py          # التذكيرات
│   └── 📄 dialogs.py            # الحوارات
└── 📁 utils/                    # الأدوات المساعدة
    └── 📄 __init__.py           # دوال مساعدة
```

## 🎯 **الوظائف الرئيسية:**

### 1. **إدارة العملاء** (`ui/clients.py`)
- ✅ إضافة وتعديل وحذف العملاء
- ✅ إدارة أرقام الهواتف المتعددة
- ✅ تتبع الأرصدة والمدفوعات
- ✅ البحث والفلترة المتقدمة
- ✅ تصدير البيانات

### 2. **إدارة الموردين** (`ui/suppliers.py`)
- ✅ إضافة وتعديل وحذف الموردين
- ✅ إدارة أرقام الهواتف المتعددة
- ✅ تتبع الأرصدة والمدفوعات
- ✅ ربط المصروفات بالموردين
- ✅ البحث والفلترة المتقدمة

### 3. **إدارة الموظفين** (`ui/employees.py`)
- ✅ إضافة وتعديل وحذف الموظفين
- ✅ إدارة الرواتب والأجور اليومية
- ✅ تتبع تواريخ التوظيف
- ✅ إدارة أرقام الهواتف المتعددة
- ✅ البحث والفلترة المتقدمة

### 4. **إدارة المصروفات** (`ui/expenses.py`)
- ✅ تسجيل المصروفات المختلفة
- ✅ تصنيف المصروفات
- ✅ ربط المصروفات بالموردين والمشاريع
- ✅ البحث والفلترة بالتاريخ والنوع
- ✅ تقارير المصروفات

### 5. **إدارة الإيرادات** (`ui/revenues.py`)
- ✅ تسجيل الإيرادات المختلفة
- ✅ تصنيف الإيرادات
- ✅ ربط الإيرادات بالعملاء والمشاريع
- ✅ البحث والفلترة بالتاريخ والنوع
- ✅ تقارير الإيرادات

### 6. **إدارة الفواتير** (`ui/invoices.py`)
- ✅ إنشاء وتعديل الفواتير
- ✅ ربط الفواتير بالعملاء
- ✅ تتبع حالات الدفع
- ✅ طباعة وتصدير الفواتير
- ✅ البحث والفلترة المتقدمة

### 7. **إدارة المشاريع** (`ui/projects.py`)
- ✅ إنشاء وإدارة المشاريع
- ✅ ربط المشاريع بالعملاء
- ✅ تتبع الميزانيات والتكاليف
- ✅ إدارة مراحل المشروع
- ✅ ربط الوثائق والعقارات

### 8. **إدارة المخزون** (`ui/inventory.py`)
- ✅ إدارة المنتجات والمواد
- ✅ تتبع الكميات والأسعار
- ✅ إدارة الحد الأدنى للمخزون
- ✅ ربط المنتجات بالموردين
- ✅ تقارير المخزون

### 9. **التقارير** (`ui/reports.py`)
- ✅ تقارير المبيعات والمشتريات
- ✅ تقارير الأرباح والخسائر
- ✅ تقارير العملاء والموردين
- ✅ تقارير المصروفات والإيرادات
- ✅ تقارير المشاريع والمخزون
- ✅ تصدير وطباعة التقارير

### 10. **الإشعارات والتذكيرات** (`ui/notifications.py`, `ui/reminders.py`)
- ✅ إشعارات المدفوعات المستحقة
- ✅ تذكيرات المواعيد المهمة
- ✅ تنبيهات المخزون المنخفض
- ✅ إشعارات انتهاء المشاريع

## 🎨 **التصميم الموحد:**

### **الأنماط** (`ui/unified_styles.py`)
- ✅ ألوان موحدة عبر التطبيق
- ✅ خطوط وأحجام متسقة
- ✅ أزرار وجداول موحدة
- ✅ حوارات وإطارات متسقة

### **المكونات المشتركة**
- ✅ `StyledButton` - أزرار موحدة
- ✅ `StyledTable` - جداول موحدة
- ✅ `StyledLabel` - تسميات موحدة
- ✅ `BaseDialog` - حوارات موحدة

## 🗄️ **قاعدة البيانات:**

### **النماذج الرئيسية** (`database.py`)
- ✅ `Client` - العملاء
- ✅ `Supplier` - الموردين
- ✅ `Employee` - الموظفين
- ✅ `Expense` - المصروفات
- ✅ `Revenue` - الإيرادات
- ✅ `Invoice` - الفواتير
- ✅ `Project` - المشاريع
- ✅ `Property` - العقارات
- ✅ `Inventory` - المخزون
- ✅ `Notification` - الإشعارات
- ✅ `Reminder` - التذكيرات

### **النماذج المساعدة**
- ✅ `ClientPhone` - هواتف العملاء
- ✅ `SupplierPhone` - هواتف الموردين
- ✅ `EmployeePhone` - هواتف الموظفين
- ✅ `Document` - الوثائق
- ✅ `User` - المستخدمين
- ✅ `Setting` - الإعدادات

## 🔧 **الأدوات المساعدة:**

### **دوال المساعدة** (`utils/__init__.py`)
- ✅ `show_info_message()` - رسائل المعلومات
- ✅ `show_error_message()` - رسائل الأخطاء
- ✅ `show_confirmation_message()` - رسائل التأكيد
- ✅ `format_currency()` - تنسيق العملة
- ✅ `format_date()` - تنسيق التاريخ
- ✅ `is_valid_email()` - التحقق من الإيميل
- ✅ `is_valid_phone()` - التحقق من الهاتف
- ✅ `qdate_to_datetime()` - تحويل التواريخ
- ✅ `datetime_to_qdate()` - تحويل التواريخ

### **تحسين الأداء** (`performance_optimizer.py`)
- ✅ `optimize_memory()` - تحسين الذاكرة
- ✅ `optimize_database()` - تحسين قاعدة البيانات
- ✅ `apply_performance_optimizations()` - تطبيق التحسينات

## 📋 **متطلبات النظام:**

```bash
PyQt5>=5.15.0
SQLAlchemy>=1.4.0
```

## 🚀 **كيفية التشغيل:**

```bash
# تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل البرنامج
python main.py
```

## 🔐 **معلومات الدخول:**
- **اسم المستخدم:** admin
- **كلمة المرور:** admin

---
**📅 آخر تحديث: 2024**
**🏢 برنامج المحاسبة للمكتب الهندسي - الإصدار النهائي المنظف**
